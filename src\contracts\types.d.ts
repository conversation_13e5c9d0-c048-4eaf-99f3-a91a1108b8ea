import { Provider, Signer, Contract, ContractTransactionResponse, BigNumberish } from 'ethers';
import { 
  NFTMetadata, 
  ResolutionRecord, 
  ReverseRecord, 
  AirdropInfo, 
  MintEligibility,
  BatchResolutionResult,
  TransferCallback,
  NameResolvedCallback
} from '../types';

// ==================== Registry Contract ====================

export declare class Registry {
  address: string;
  contract: Contract;

  constructor(address: string, providerOrSigner: Provider | Signer);

  // Read-only functions
  name(): Promise<string>;
  symbol(): Promise<string>;
  totalSupply(): Promise<bigint>;
  ownerOf(tokenId: BigNumberish): Promise<string>;
  balanceOf(owner: string): Promise<bigint>;
  tokenOfOwnerByIndex(owner: string, index: BigNumberish): Promise<bigint>;
  tokenByIndex(index: BigNumberish): Promise<bigint>;
  tokenURI(tokenId: BigNumberish): Promise<string>;
  getApproved(tokenId: BigNumberish): Promise<string>;
  isApprovedForAll(owner: string, operator: string): Promise<boolean>;
  
  // ODude-specific functions
  getTokenId(name: string): Promise<bigint>;
  nameOf(tokenId: BigNumberish): Promise<string>;
  getOwnerByName(name: string): Promise<string>;
  getNFTMetadata(tokenId: BigNumberish): Promise<NFTMetadata>;
  
  // Batch operations
  getMultipleNames(tokenIds: BigNumberish[]): Promise<string[]>;
  getMultipleOwners(tokenIds: BigNumberish[]): Promise<string[]>;
  getMultipleTokenInfo(tokenIds: BigNumberish[]): Promise<Array<{
    tokenId: string;
    name: string;
    owner: string;
    tokenURI: string;
  }>>;

  // Write functions
  mintDomain(
    tokenId: BigNumberish,
    name: string,
    metadataURI: string,
    to: string,
    options?: { value?: BigNumberish }
  ): Promise<ContractTransactionResponse>;
  
  setReverse(tokenId: BigNumberish): Promise<ContractTransactionResponse>;
  approve(to: string, tokenId: BigNumberish): Promise<ContractTransactionResponse>;
  setApprovalForAll(operator: string, approved: boolean): Promise<ContractTransactionResponse>;
  transferFrom(from: string, to: string, tokenId: BigNumberish): Promise<ContractTransactionResponse>;
  safeTransferFrom(from: string, to: string, tokenId: BigNumberish, data?: string): Promise<ContractTransactionResponse>;

  // Event listeners
  onTransfer(callback: TransferCallback): void;
  onTLDMinted(callback: (tokenId: bigint, name: string, owner: string) => void): void;
  removeAllListeners(): void;
}

// ==================== Resolver Contract ====================

export declare class Resolver {
  address: string;
  contract: Contract;

  constructor(address: string, providerOrSigner: Provider | Signer);

  // Read-only functions
  resolve(name: string): Promise<string>;
  reverse(address: string): Promise<string>;
  nameExists(name: string): Promise<boolean>;
  hasReverse(address: string): Promise<boolean>;
  getResolutionRecord(name: string): Promise<ResolutionRecord>;
  getReverseRecord(address: string): Promise<ReverseRecord>;
  
  // Batch operations
  resolveMultiple(names: string[]): Promise<BatchResolutionResult[]>;
  reverseMultiple(addresses: string[]): Promise<Array<{
    address: string;
    name: string | null;
    resolved: boolean;
    error?: string;
  }>>;
  checkMultipleNamesExist(names: string[]): Promise<Array<{
    name: string;
    exists: boolean;
    error?: string;
  }>>;

  // Write functions
  setName(name: string, address: string): Promise<ContractTransactionResponse>;
  setReverse(name: string): Promise<ContractTransactionResponse>;

  // Event listeners
  onNameSet(callback: NameResolvedCallback): void;
  removeAllListeners(): void;
}

// ==================== TLD Contract ====================

export declare class TLD {
  address: string;
  contract: Contract;

  constructor(address: string, providerOrSigner: Provider | Signer);

  // Read-only functions
  getBaseTLDPrice(): Promise<bigint>;
  getTLDPrice(tldTokenId: BigNumberish): Promise<bigint>;
  getCommission(tldTokenId: BigNumberish): Promise<bigint>;
  getTLDOwner(tldTokenId: BigNumberish): Promise<string>;
  isTLDActive(tldTokenId: BigNumberish): Promise<boolean>;
  getTLDName(tldTokenId: BigNumberish): Promise<string>;
  getErcToken(tldTokenId: BigNumberish): Promise<string>;
  getTokenPrice(tldTokenId: BigNumberish): Promise<bigint>;
  getTLDToken(tldTokenId: BigNumberish): Promise<string>;
  
  // Domain minting functions
  checkMintEligibility(domainName: string): Promise<MintEligibility>;
  estimateMintCost(domainName: string): Promise<bigint>;

  // Write functions
  setTLDPrice(tldTokenId: BigNumberish, price: BigNumberish): Promise<ContractTransactionResponse>;
  setCommission(tldTokenId: BigNumberish, commission: BigNumberish): Promise<ContractTransactionResponse>;
  setErcToken(tldTokenId: BigNumberish, tokenAddress: string): Promise<ContractTransactionResponse>;
  setTokenPrice(tldTokenId: BigNumberish, price: BigNumberish): Promise<ContractTransactionResponse>;
  mintDomain(
    domainName: string,
    to: string,
    options?: { value?: BigNumberish }
  ): Promise<ContractTransactionResponse>;

  // Event listeners
  removeAllListeners(): void;
}

// ==================== RWAirdrop Contract ====================

export declare class RWAirdrop {
  address: string;
  contract: Contract;

  constructor(address: string, providerOrSigner: Provider | Signer);

  // Read-only functions
  isEligible(address: string): Promise<boolean>;
  getClaimableAmount(address: string): Promise<bigint>;
  hasClaimed(address: string): Promise<boolean>;
  getAirdropInfo(address: string): Promise<AirdropInfo>;
  getTotalClaimed(): Promise<bigint>;
  getTotalAirdropAmount(): Promise<bigint>;

  // Write functions
  mintDomain(): Promise<ContractTransactionResponse>;

  // Event listeners
  removeAllListeners(): void;
}
