// ========== VIEW FUNCTIONS (read-only) ==========
function UPGRADE_INTERFACE_VERSION() view returns (string);
// Returns the version string of the upgradeable interface.

function getBaseTLDPrice() view returns (uint256);
// Returns the base price for minting or registering new TLDs.

function getDefaultCommission() view returns (uint256);
// Returns the default commission percentage applied to TLDs.

function getRegistryContract() view returns (address);
// Returns the address of the linked Registry contract.

function getTLD(uint256 id) view returns (TLDInfo);
// Returns all information about a TLD by its ID (name, price, owner, etc.).

function getTLDCommission(uint256 id) view returns (uint256);
// Returns the custom commission for a specific TLD.

function getTLDId(string name) view returns (uint256);
// Returns the unique ID assigned to a TLD name.

function getTLDOwner(uint256 id) view returns (address);
// Returns the owner address of a specific TLD.

function getTLDPrice(uint256 id) view returns (uint256);
// Returns the price set for a specific TLD.

function getTLDToken(uint256 id) view returns (address);
// Returns the ERC20 token address associated with a TLD (if any).

function getTLDsByOwner(address owner) view returns (uint256[]);
// Returns a list of all TLD IDs owned by a given address.

function owner() view returns (address);
// Returns the owner (admin) of the TLD contract.

function proxiableUUID() view returns (bytes32);
// Returns the UUID used by UUPS proxy for upgrade compatibility.

function tldExists(uint256 id) view returns (bool);
// Checks whether a TLD with the given ID exists or not.


// ========== WRITE FUNCTIONS (state-changing) ==========
function initialize(address initialOwner);
// Initializes the contract and sets the initial owner (only once).

function registerTLD(uint256 id, string name, address tldOwner);
// Registers a new Top-Level Domain (TLD) with given ID, name, and owner.

function setBaseTLDPrice(uint256 price);
// Sets the base price for creating new TLDs (only callable by owner).

function setDefaultCommission(uint256 commission);
// Sets the default commission rate applied to all TLDs (only owner).

function setRegistryContract(address registryContract);
// Links the TLD contract with the Registry contract (only owner).

function setTLDCommission(uint256 id, uint256 commission);
// Sets a specific commission rate for a given TLD.

function setTLDPrice(uint256 id, uint256 price);
// Sets a specific minting price for a given TLD.

function setTLDToken(uint256 id, address token);
// Binds a custom ERC20 token as the payment method for a TLD.

function transferOwnership(address newOwner);
// Transfers ownership of the entire TLD contract to a new admin.

function transferTLDOwnership(uint256 id, address newOwner);
// Transfers ownership of a specific TLD to another address.

function renounceOwnership();
// Removes the current owner, making the contract ownerless.

function upgradeToAndCall(address newImplementation, bytes data) payable;
// Upgrades the contract implementation (UUPS pattern) and optionally calls setup logic.
